#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cursor AI 无限使用工具 - 一键安装脚本
自动下载和配置所需的组件
"""

import os
import sys
import platform
import subprocess
import urllib.request
import json
from pathlib import Path

class CursorToolInstaller:
    def __init__(self):
        self.system = platform.system()
        self.python_cmd = self._get_python_command()
        
    def _get_python_command(self):
        """获取Python命令"""
        commands = ['python3', 'python']
        for cmd in commands:
            try:
                result = subprocess.run([cmd, '--version'], 
                                      capture_output=True, text=True)
                if result.returncode == 0:
                    return cmd
            except FileNotFoundError:
                continue
        return None
    
    def print_banner(self):
        """打印安装横幅"""
        banner = """
╔══════════════════════════════════════════════════════════════╗
║              Cursor AI 无限使用工具 - 安装程序                ║
║                                                              ║
║  正在为您安装和配置Cursor无限使用工具...                      ║
╚══════════════════════════════════════════════════════════════╝
        """
        print(banner)
    
    def check_python(self):
        """检查Python环境"""
        print("🔍 检查Python环境...")
        
        if not self.python_cmd:
            print("❌ 未找到Python，请先安装Python 3.6+")
            print("下载地址: https://www.python.org/downloads/")
            return False
        
        try:
            result = subprocess.run([self.python_cmd, '--version'], 
                                  capture_output=True, text=True)
            version = result.stdout.strip()
            print(f"✅ 找到Python: {version}")
            
            # 检查版本
            version_parts = version.split()[1].split('.')
            major, minor = int(version_parts[0]), int(version_parts[1])
            
            if major < 3 or (major == 3 and minor < 6):
                print("❌ Python版本过低，需要3.6+")
                return False
                
            return True
            
        except Exception as e:
            print(f"❌ Python检查失败: {e}")
            return False
    
    def check_cursor_installation(self):
        """检查Cursor是否已安装"""
        print("🔍 检查Cursor安装...")
        
        cursor_paths = []
        if self.system == "Windows":
            cursor_paths = [
                os.path.expandvars("%APPDATA%\\Cursor"),
                os.path.expandvars("%LOCALAPPDATA%\\Programs\\cursor"),
                "C:\\Program Files\\Cursor",
                "C:\\Program Files (x86)\\Cursor"
            ]
        elif self.system == "Darwin":  # macOS
            cursor_paths = [
                "/Applications/Cursor.app",
                os.path.expanduser("~/Applications/Cursor.app"),
                os.path.expanduser("~/Library/Application Support/Cursor")
            ]
        else:  # Linux
            cursor_paths = [
                "/usr/local/bin/cursor",
                "/usr/bin/cursor",
                os.path.expanduser("~/.local/share/cursor"),
                os.path.expanduser("~/.config/Cursor")
            ]
        
        for path in cursor_paths:
            if os.path.exists(path):
                print(f"✅ 找到Cursor安装: {path}")
                return True
        
        print("⚠️  未找到Cursor安装，请确保已安装Cursor编辑器")
        print("下载地址: https://cursor.sh/")
        return False
    
    def create_desktop_shortcut(self):
        """创建桌面快捷方式"""
        print("🔗 创建桌面快捷方式...")
        
        try:
            desktop_path = ""
            if self.system == "Windows":
                desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
                shortcut_content = f"""@echo off
cd /d "{os.getcwd()}"
{self.python_cmd} cursor_pro_unlimited.py
pause"""
                shortcut_path = os.path.join(desktop_path, "Cursor无限使用工具.bat")
                with open(shortcut_path, 'w', encoding='utf-8') as f:
                    f.write(shortcut_content)
                    
            elif self.system == "Darwin":  # macOS
                desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
                shortcut_content = f"""#!/bin/bash
cd "{os.getcwd()}"
{self.python_cmd} cursor_pro_unlimited.py"""
                shortcut_path = os.path.join(desktop_path, "Cursor无限使用工具.command")
                with open(shortcut_path, 'w', encoding='utf-8') as f:
                    f.write(shortcut_content)
                os.chmod(shortcut_path, 0o755)
                
            else:  # Linux
                desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
                shortcut_content = f"""[Desktop Entry]
Version=1.0
Type=Application
Name=Cursor无限使用工具
Comment=Cursor AI unlimited usage tool
Exec={self.python_cmd} {os.path.join(os.getcwd(), 'cursor_pro_unlimited.py')}
Icon=utilities-terminal
Terminal=true
Categories=Utility;"""
                shortcut_path = os.path.join(desktop_path, "cursor-unlimited.desktop")
                with open(shortcut_path, 'w', encoding='utf-8') as f:
                    f.write(shortcut_content)
                os.chmod(shortcut_path, 0o755)
            
            if os.path.exists(shortcut_path):
                print(f"✅ 桌面快捷方式已创建: {shortcut_path}")
            else:
                print("⚠️  桌面快捷方式创建失败")
                
        except Exception as e:
            print(f"⚠️  创建桌面快捷方式时出错: {e}")
    
    def create_start_menu_entry(self):
        """创建开始菜单项 (仅Windows)"""
        if self.system != "Windows":
            return
            
        print("📋 创建开始菜单项...")
        
        try:
            start_menu_path = os.path.expandvars("%APPDATA%\\Microsoft\\Windows\\Start Menu\\Programs")
            folder_path = os.path.join(start_menu_path, "Cursor工具")
            os.makedirs(folder_path, exist_ok=True)
            
            # 创建主程序快捷方式
            shortcut_content = f"""@echo off
cd /d "{os.getcwd()}"
{self.python_cmd} cursor_pro_unlimited.py
pause"""
            shortcut_path = os.path.join(folder_path, "Cursor无限使用工具.bat")
            with open(shortcut_path, 'w', encoding='utf-8') as f:
                f.write(shortcut_content)
            
            # 创建基础版快捷方式
            basic_shortcut_content = f"""@echo off
cd /d "{os.getcwd()}"
{self.python_cmd} cursor_unlimited.py
pause"""
            basic_shortcut_path = os.path.join(folder_path, "Cursor工具(基础版).bat")
            with open(basic_shortcut_path, 'w', encoding='utf-8') as f:
                f.write(basic_shortcut_content)
            
            print(f"✅ 开始菜单项已创建: {folder_path}")
            
        except Exception as e:
            print(f"⚠️  创建开始菜单项时出错: {e}")
    
    def setup_auto_update(self):
        """设置自动更新检查"""
        print("🔄 配置自动更新...")
        
        update_config = {
            "auto_check": True,
            "update_interval": 7,  # 天
            "last_check": 0,
            "version": "2.0"
        }
        
        try:
            with open("update_config.json", 'w', encoding='utf-8') as f:
                json.dump(update_config, f, indent=2)
            print("✅ 自动更新配置完成")
        except Exception as e:
            print(f"⚠️  配置自动更新时出错: {e}")
    
    def run_initial_test(self):
        """运行初始测试"""
        print("🧪 运行初始测试...")
        
        try:
            # 测试基础工具
            result = subprocess.run([self.python_cmd, '-c', 
                                   'import cursor_unlimited; print("基础工具导入成功")'],
                                  capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                print("✅ 基础工具测试通过")
            else:
                print("⚠️  基础工具测试失败")
            
            # 测试高级工具
            result = subprocess.run([self.python_cmd, '-c', 
                                   'import cursor_pro_unlimited; print("高级工具导入成功")'],
                                  capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                print("✅ 高级工具测试通过")
            else:
                print("⚠️  高级工具测试失败")
                
        except subprocess.TimeoutExpired:
            print("⚠️  测试超时")
        except Exception as e:
            print(f"⚠️  测试过程出错: {e}")
    
    def show_completion_info(self):
        """显示安装完成信息"""
        info = """
╔══════════════════════════════════════════════════════════════╗
║                        安装完成！                            ║
║                                                              ║
║  🎉 Cursor AI 无限使用工具已成功安装                          ║
║                                                              ║
║  使用方法:                                                    ║
║  1. 双击桌面快捷方式启动工具                                  ║
║  2. 或运行: python cursor_pro_unlimited.py                   ║
║                                                              ║
║  推荐使用方法:                                                ║
║  • 首次使用选择 "机器ID重置法"                                ║
║  • 如果失败可尝试 "完全重置法"                                ║
║                                                              ║
║  ⚠️  注意: 仅供学习研究使用，请支持正版软件                   ║
╚══════════════════════════════════════════════════════════════╝
        """
        print(info)
    
    def install(self):
        """执行安装流程"""
        self.print_banner()
        
        # 检查Python环境
        if not self.check_python():
            return False
        
        # 检查Cursor安装
        self.check_cursor_installation()
        
        # 创建快捷方式
        self.create_desktop_shortcut()
        self.create_start_menu_entry()
        
        # 配置自动更新
        self.setup_auto_update()
        
        # 运行测试
        self.run_initial_test()
        
        # 显示完成信息
        self.show_completion_info()
        
        return True

def main():
    """主函数"""
    try:
        installer = CursorToolInstaller()
        success = installer.install()
        
        if success:
            input("\n按回车键退出安装程序...")
        else:
            input("\n安装失败，按回车键退出...")
            
    except KeyboardInterrupt:
        print("\n\n安装被用户中断")
    except Exception as e:
        print(f"\n安装过程出错: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
