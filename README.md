# Cursor AI 无限使用工具

一个强大的工具集，帮助您绕过Cursor AI编辑器的使用限制，实现无限使用。

## 🚀 功能特性

- **多种重置方法**: 提供5种不同的重置策略
- **跨平台支持**: 支持Windows、macOS、Linux
- **自动化操作**: 一键重置，无需手动操作
- **安全备份**: 自动备份配置，支持一键恢复
- **智能检测**: 自动检测Cursor状态和配置

## 📁 文件说明

- `cursor_unlimited.py` - 基础版工具
- `cursor_pro_unlimited.py` - 高级版工具(推荐)
- `cursor_unlimited.bat` - Windows启动脚本
- `cursor_unlimited.sh` - macOS/Linux启动脚本

## 🛠️ 安装要求

- Python 3.6 或更高版本
- 已安装Cursor AI编辑器

## 📖 使用方法

### Windows用户

1. 双击运行 `cursor_unlimited.bat`
2. 或者在命令行中运行：
   ```cmd
   python cursor_pro_unlimited.py
   ```

### macOS/Linux用户

1. 给脚本添加执行权限：
   ```bash
   chmod +x cursor_unlimited.sh
   ```
2. 运行脚本：
   ```bash
   ./cursor_unlimited.sh
   ```
3. 或者直接运行Python脚本：
   ```bash
   python3 cursor_pro_unlimited.py
   ```

## 🔧 重置方法说明

### 1. 机器ID重置法 (推荐)
- 生成新的机器指纹
- 修改telemetry相关ID
- 成功率高，风险较低

### 2. 配置文件清理法
- 清理storage.json中的试用相关配置
- 保留基本设置
- 适合轻度重置

### 3. 用户数据重置法
- 清理用户工作区数据
- 删除日志和缓存
- 重置用户会话

### 4. 完全重置法
- 删除所有配置文件
- 重新创建配置目录
- 最彻底的重置方法

### 5. 自动循环重置
- 定时自动重置
- 可设置重置间隔
- 适合长期使用

## ⚡ 快速开始

1. **下载工具**
   ```bash
   git clone https://github.com/your-repo/cursor-unlimited-tool.git
   cd cursor-unlimited-tool
   ```

2. **运行高级版工具**
   ```bash
   python cursor_pro_unlimited.py
   ```

3. **选择重置方法**
   - 推荐首次使用选择"1. 机器ID重置法"
   - 如果方法1失败，可尝试"4. 完全重置法"

4. **重启Cursor**
   - 重置完成后重新启动Cursor
   - 登录您的账户即可享受新的试用期

## 🔍 故障排除

### 常见问题

**Q: 提示"未找到Cursor配置目录"**
A: 确保已正确安装Cursor编辑器，或选择创建配置目录

**Q: 重置后仍然提示试用期结束**
A: 尝试使用"完全重置法"或清理浏览器缓存后重新登录

**Q: 工具运行出错**
A: 确保以管理员权限运行，并关闭所有Cursor进程

**Q: 重置后设置丢失**
A: 可以使用备份恢复功能恢复之前的配置

### 高级技巧

1. **定期备份**: 在重置前建议手动备份重要配置
2. **多账户轮换**: 可以准备多个邮箱账户轮换使用
3. **虚拟机使用**: 在虚拟机中使用可以更好地隔离环境

## 📋 使用流程

```
1. 关闭Cursor编辑器
   ↓
2. 运行重置工具
   ↓
3. 选择重置方法
   ↓
4. 等待重置完成
   ↓
5. 重新启动Cursor
   ↓
6. 登录账户享受新试用期
```

## ⚠️ 注意事项

- **仅供学习研究使用**，请支持正版软件
- 使用前请备份重要数据
- 某些方法可能违反软件服务条款
- 建议在虚拟机或测试环境中使用
- 工具更新可能影响重置效果

## 🔒 安全说明

- 工具不会收集任何个人信息
- 所有操作均在本地进行
- 建议定期更新工具以获得最佳效果
- 使用时请关闭杀毒软件的实时保护

## 🆘 技术支持

如果遇到问题，请：

1. 检查Python版本是否符合要求
2. 确保以管理员权限运行
3. 查看错误日志信息
4. 尝试不同的重置方法

## 📄 免责声明

本工具仅供学习和研究目的使用。使用者应当：

- 遵守当地法律法规
- 尊重软件开发者的劳动成果
- 承担使用工具的所有风险
- 在有能力的情况下支持正版软件

开发者不对使用本工具造成的任何后果承担责任。

## 🎯 更新日志

### v2.0
- 新增高级版工具
- 支持5种重置方法
- 添加自动循环重置功能
- 改进备份和恢复机制

### v1.0
- 基础重置功能
- 跨平台支持
- 简单易用的界面

---

**记住：支持开发者，购买正版软件是最好的选择！** 💝
