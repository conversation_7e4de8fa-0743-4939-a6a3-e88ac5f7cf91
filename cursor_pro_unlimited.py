#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cursor AI Pro 无限使用工具 - 高级版
包含多种绕过方法和自动化功能
"""

import os
import sys
import json
import uuid
import shutil
import platform
import subprocess
import time
import hashlib
import random
import string
from pathlib import Path
from typing import Dict, List, Optional
import tempfile

class CursorProUnlimited:
    def __init__(self):
        self.system = platform.system()
        self.config_paths = self._get_config_paths()
        self.methods = {
            "1": "机器ID重置法",
            "2": "配置文件清理法", 
            "3": "用户数据重置法",
            "4": "完全重置法",
            "5": "自动循环重置"
        }
        
    def _get_config_paths(self) -> Dict[str, str]:
        """获取Cursor配置路径"""
        if self.system == "Windows":
            base_path = os.path.expandvars("%APPDATA%\\Cursor")
            local_path = os.path.expandvars("%LOCALAPPDATA%\\Cursor")
        elif self.system == "Darwin":  # macOS
            base_path = os.path.expanduser("~/Library/Application Support/Cursor")
            local_path = os.path.expanduser("~/Library/Caches/Cursor")
        else:  # Linux
            base_path = os.path.expanduser("~/.config/Cursor")
            local_path = os.path.expanduser("~/.cache/Cursor")
            
        return {
            "base": base_path,
            "local": local_path,
            "storage": os.path.join(base_path, "User", "globalStorage", "storage.json"),
            "user_data": os.path.join(base_path, "User"),
            "logs": os.path.join(base_path, "logs"),
            "cache": os.path.join(local_path, "Cache"),
            "session": os.path.join(base_path, "User", "workspaceStorage"),
            "extensions": os.path.join(base_path, "User", "extensions")
        }
    
    def print_banner(self):
        """打印工具横幅"""
        banner = """
╔══════════════════════════════════════════════════════════════╗
║                 Cursor AI Pro 无限使用工具                    ║
║                        高级版 v2.0                           ║
║                                                              ║
║  支持的重置方法:                                              ║
║  • 机器ID重置法 (推荐)                                        ║
║  • 配置文件清理法                                            ║
║  • 用户数据重置法                                            ║
║  • 完全重置法 (最彻底)                                        ║
║  • 自动循环重置                                              ║
║                                                              ║
║  ⚠️  注意: 仅供学习研究使用，请支持正版软件                   ║
╚══════════════════════════════════════════════════════════════╝
        """
        print(banner)
    
    def generate_random_string(self, length: int = 32) -> str:
        """生成随机字符串"""
        return ''.join(random.choices(string.ascii_letters + string.digits, k=length))
    
    def generate_machine_fingerprint(self) -> Dict[str, str]:
        """生成机器指纹"""
        # 生成更真实的机器ID
        mac_base = str(uuid.uuid4())
        machine_base = str(uuid.uuid4()) 
        device_base = str(uuid.uuid4())
        
        # 添加一些随机性
        timestamp = str(int(time.time()))
        random_suffix = self.generate_random_string(8)
        
        return {
            "telemetry.macMachineId": mac_base,
            "telemetry.machineId": machine_base, 
            "telemetry.devDeviceId": device_base,
            "telemetry.sessionId": str(uuid.uuid4()),
            "telemetry.instanceId": str(uuid.uuid4()),
            "workbench.telemetry.machineId": machine_base,
            "machineId": machine_base,
            "devDeviceId": device_base,
            "sessionId": str(uuid.uuid4()) + timestamp + random_suffix
        }
    
    def force_kill_cursor(self):
        """强制终止所有Cursor相关进程"""
        print("正在强制终止Cursor进程...")
        try:
            if self.system == "Windows":
                # 终止所有相关进程
                processes = ["Cursor.exe", "cursor.exe", "Code.exe", "code.exe"]
                for proc in processes:
                    subprocess.run(["taskkill", "/F", "/IM", proc], 
                                 capture_output=True, stderr=subprocess.DEVNULL)
            else:
                # Unix系统
                subprocess.run(["pkill", "-9", "-f", "cursor"], 
                             capture_output=True, stderr=subprocess.DEVNULL)
                subprocess.run(["pkill", "-9", "-f", "Cursor"], 
                             capture_output=True, stderr=subprocess.DEVNULL)
            
            time.sleep(3)  # 等待进程完全终止
            print("✓ Cursor进程已强制终止")
        except Exception as e:
            print(f"⚠️  终止进程时出现警告: {e}")
    
    def create_backup(self) -> str:
        """创建完整备份"""
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        backup_dir = f"cursor_backup_{timestamp}"
        
        try:
            if os.path.exists(self.config_paths["base"]):
                shutil.copytree(self.config_paths["base"], backup_dir)
                print(f"✓ 完整备份已创建: {backup_dir}")
                return backup_dir
        except Exception as e:
            print(f"⚠️  备份创建失败: {e}")
        return ""
    
    def method_machine_id_reset(self) -> bool:
        """方法1: 机器ID重置法"""
        print("\n🔄 执行机器ID重置法...")
        
        try:
            # 生成新的机器指纹
            new_fingerprint = self.generate_machine_fingerprint()
            
            # 修改storage.json
            storage_path = self.config_paths["storage"]
            if os.path.exists(storage_path):
                with open(storage_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
            else:
                os.makedirs(os.path.dirname(storage_path), exist_ok=True)
                config = {}
            
            # 更新所有相关ID
            config.update(new_fingerprint)
            
            # 清除试用相关数据
            trial_keys = [
                "cursor.trial", "cursor.usage", "cursor.subscription",
                "workbench.telemetry.lastSessionDate", "telemetry.lastSessionDate"
            ]
            
            for key in list(config.keys()):
                for trial_key in trial_keys:
                    if trial_key in key:
                        del config[key]
                        break
            
            with open(storage_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2)
            
            print("✓ 机器ID重置完成")
            return True
            
        except Exception as e:
            print(f"✗ 机器ID重置失败: {e}")
            return False
    
    def method_config_cleanup(self) -> bool:
        """方法2: 配置文件清理法"""
        print("\n🧹 执行配置文件清理法...")
        
        try:
            # 清理storage.json中的特定键
            storage_path = self.config_paths["storage"]
            if os.path.exists(storage_path):
                with open(storage_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                # 要清理的键模式
                cleanup_patterns = [
                    "cursor", "trial", "usage", "subscription", "telemetry",
                    "workbench.telemetry", "auth", "session", "user"
                ]
                
                original_keys = list(config.keys())
                for key in original_keys:
                    for pattern in cleanup_patterns:
                        if pattern.lower() in key.lower():
                            del config[key]
                            break
                
                with open(storage_path, 'w', encoding='utf-8') as f:
                    json.dump(config, f, indent=2)
            
            print("✓ 配置文件清理完成")
            return True
            
        except Exception as e:
            print(f"✗ 配置文件清理失败: {e}")
            return False
    
    def method_user_data_reset(self) -> bool:
        """方法3: 用户数据重置法"""
        print("\n🗂️  执行用户数据重置法...")
        
        try:
            paths_to_clean = [
                self.config_paths["logs"],
                self.config_paths["cache"], 
                self.config_paths["session"]
            ]
            
            for path in paths_to_clean:
                if os.path.exists(path):
                    shutil.rmtree(path)
                    print(f"✓ 已清理: {os.path.basename(path)}")
            
            # 清理用户工作区数据
            user_data_path = self.config_paths["user_data"]
            if os.path.exists(user_data_path):
                for item in os.listdir(user_data_path):
                    if item not in ["globalStorage"]:  # 保留globalStorage
                        item_path = os.path.join(user_data_path, item)
                        if os.path.isdir(item_path):
                            shutil.rmtree(item_path)
                        else:
                            os.remove(item_path)
                        print(f"✓ 已清理用户数据: {item}")
            
            print("✓ 用户数据重置完成")
            return True
            
        except Exception as e:
            print(f"✗ 用户数据重置失败: {e}")
            return False
    
    def method_complete_reset(self) -> bool:
        """方法4: 完全重置法"""
        print("\n💥 执行完全重置法...")
        
        try:
            # 删除整个配置目录
            if os.path.exists(self.config_paths["base"]):
                shutil.rmtree(self.config_paths["base"])
                print("✓ 主配置目录已删除")
            
            if os.path.exists(self.config_paths["local"]):
                shutil.rmtree(self.config_paths["local"])
                print("✓ 本地缓存目录已删除")
            
            # 重新创建基本目录结构
            os.makedirs(os.path.dirname(self.config_paths["storage"]), exist_ok=True)
            
            # 创建新的配置文件
            new_config = self.generate_machine_fingerprint()
            with open(self.config_paths["storage"], 'w', encoding='utf-8') as f:
                json.dump(new_config, f, indent=2)
            
            print("✓ 完全重置完成")
            return True
            
        except Exception as e:
            print(f"✗ 完全重置失败: {e}")
            return False
    
    def method_auto_cycle_reset(self):
        """方法5: 自动循环重置"""
        print("\n🔄 启动自动循环重置模式...")
        print("此模式将每隔指定时间自动重置试用期")
        
        try:
            interval = input("请输入重置间隔(小时，默认24): ").strip()
            interval = int(interval) if interval.isdigit() else 24
            
            cycles = input("请输入循环次数(默认无限): ").strip()
            cycles = int(cycles) if cycles.isdigit() else -1
            
            count = 0
            while cycles == -1 or count < cycles:
                count += 1
                print(f"\n--- 第 {count} 次自动重置 ---")
                
                self.force_kill_cursor()
                success = self.method_machine_id_reset()
                
                if success:
                    print(f"✓ 第 {count} 次重置成功")
                else:
                    print(f"✗ 第 {count} 次重置失败")
                
                if cycles == -1 or count < cycles:
                    print(f"等待 {interval} 小时后进行下次重置...")
                    time.sleep(interval * 3600)  # 转换为秒
                    
        except KeyboardInterrupt:
            print("\n自动循环重置已停止")
        except Exception as e:
            print(f"自动循环重置出错: {e}")
    
    def execute_reset_method(self, method: str) -> bool:
        """执行指定的重置方法"""
        # 强制终止Cursor
        self.force_kill_cursor()
        
        # 创建备份
        backup_dir = self.create_backup()
        
        success = False
        if method == "1":
            success = self.method_machine_id_reset()
        elif method == "2":
            success = self.method_config_cleanup()
        elif method == "3":
            success = self.method_user_data_reset()
        elif method == "4":
            success = self.method_complete_reset()
        elif method == "5":
            self.method_auto_cycle_reset()
            return True
        
        if success:
            print(f"\n🎉 {self.methods[method]} 执行成功！")
            print("请重新启动Cursor并登录账户")
            
            # 询问是否删除备份
            if backup_dir:
                delete_backup = input("\n是否删除备份文件? (y/N): ").strip().lower()
                if delete_backup == 'y':
                    shutil.rmtree(backup_dir)
                    print("✓ 备份文件已删除")
        else:
            print(f"\n❌ {self.methods[method]} 执行失败")
            if backup_dir:
                print(f"可以从备份恢复: {backup_dir}")
        
        return success
    
    def show_menu(self):
        """显示主菜单"""
        while True:
            print("\n" + "="*60)
            print("请选择重置方法:")
            for key, value in self.methods.items():
                print(f"{key}. {value}")
            print("6. 检查Cursor状态")
            print("7. 恢复备份")
            print("0. 退出")
            print("="*60)
            
            choice = input("请输入选项 (0-7): ").strip()
            
            if choice in self.methods:
                confirm = input(f"\n确认执行 '{self.methods[choice]}'? (y/N): ").strip().lower()
                if confirm == 'y':
                    self.execute_reset_method(choice)
            elif choice == "6":
                self.check_status()
            elif choice == "7":
                self.restore_backup()
            elif choice == "0":
                print("感谢使用！")
                break
            else:
                print("无效选项，请重新选择")
    
    def check_status(self):
        """检查Cursor状态"""
        print("\n📊 Cursor状态检查:")
        
        # 检查进程
        running = self.check_cursor_running()
        print(f"运行状态: {'🟢 运行中' if running else '🔴 未运行'}")
        
        # 检查配置文件
        storage_exists = os.path.exists(self.config_paths["storage"])
        print(f"配置文件: {'🟢 存在' if storage_exists else '🔴 不存在'}")
        
        # 检查配置目录大小
        if os.path.exists(self.config_paths["base"]):
            size = self.get_dir_size(self.config_paths["base"])
            print(f"配置目录大小: {size:.2f} MB")
        
        print(f"配置路径: {self.config_paths['base']}")
    
    def get_dir_size(self, path: str) -> float:
        """获取目录大小(MB)"""
        total_size = 0
        try:
            for dirpath, dirnames, filenames in os.walk(path):
                for filename in filenames:
                    filepath = os.path.join(dirpath, filename)
                    if os.path.exists(filepath):
                        total_size += os.path.getsize(filepath)
        except:
            pass
        return total_size / (1024 * 1024)
    
    def check_cursor_running(self) -> bool:
        """检查Cursor是否运行"""
        try:
            if self.system == "Windows":
                result = subprocess.run(
                    ["tasklist", "/FI", "IMAGENAME eq Cursor.exe"],
                    capture_output=True, text=True
                )
                return "Cursor.exe" in result.stdout
            else:
                result = subprocess.run(
                    ["pgrep", "-f", "cursor"],
                    capture_output=True, text=True
                )
                return bool(result.stdout.strip())
        except:
            return False
    
    def restore_backup(self):
        """恢复备份"""
        # 查找备份目录
        backup_dirs = [d for d in os.listdir('.') if d.startswith('cursor_backup_')]
        
        if not backup_dirs:
            print("✗ 未找到备份目录")
            return
        
        print("\n可用的备份:")
        for i, backup_dir in enumerate(backup_dirs, 1):
            print(f"{i}. {backup_dir}")
        
        try:
            choice = input("请选择要恢复的备份 (输入序号): ").strip()
            index = int(choice) - 1
            
            if 0 <= index < len(backup_dirs):
                backup_dir = backup_dirs[index]
                
                # 强制终止Cursor
                self.force_kill_cursor()
                
                # 删除当前配置
                if os.path.exists(self.config_paths["base"]):
                    shutil.rmtree(self.config_paths["base"])
                
                # 恢复备份
                shutil.copytree(backup_dir, self.config_paths["base"])
                print(f"✓ 已从 {backup_dir} 恢复配置")
            else:
                print("无效的选择")
                
        except (ValueError, IndexError):
            print("无效的输入")
        except Exception as e:
            print(f"恢复失败: {e}")

def main():
    """主函数"""
    try:
        tool = CursorProUnlimited()
        tool.print_banner()
        
        # 检查配置路径
        if not os.path.exists(tool.config_paths["base"]):
            print(f"⚠️  未找到Cursor配置目录: {tool.config_paths['base']}")
            print("请确保已安装Cursor编辑器")
            
            create_config = input("是否创建配置目录? (y/N): ").strip().lower()
            if create_config == 'y':
                os.makedirs(tool.config_paths["base"], exist_ok=True)
                print("✓ 配置目录已创建")
            else:
                return
        
        tool.show_menu()
        
    except KeyboardInterrupt:
        print("\n\n程序被用户中断")
    except Exception as e:
        print(f"\n程序运行出错: {e}")

if __name__ == "__main__":
    main()
