#!/bin/bash

# Cursor AI 无限使用工具 - Shell启动脚本

echo ""
echo "╔══════════════════════════════════════════════════════════════╗"
echo "║                    Cursor AI 无限使用工具                      ║"
echo "║                                                              ║"
echo "║  正在启动Python脚本...                                        ║"
echo "╚══════════════════════════════════════════════════════════════╝"
echo ""

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo "✗ 未检测到Python，请先安装Python 3.6+"
        echo "macOS: brew install python3"
        echo "Ubuntu/Debian: sudo apt install python3"
        echo "CentOS/RHEL: sudo yum install python3"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

# 检查Python版本
PYTHON_VERSION=$($PYTHON_CMD --version 2>&1 | awk '{print $2}' | cut -d. -f1,2)
REQUIRED_VERSION="3.6"

if [ "$(printf '%s\n' "$REQUIRED_VERSION" "$PYTHON_VERSION" | sort -V | head -n1)" != "$REQUIRED_VERSION" ]; then
    echo "✗ Python版本过低，需要Python 3.6+，当前版本: $PYTHON_VERSION"
    exit 1
fi

# 运行Python脚本
$PYTHON_CMD cursor_unlimited.py

echo ""
echo "按任意键退出..."
read -n 1
