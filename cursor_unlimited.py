#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cursor AI 无限使用工具
支持多种方法绕过Cursor的使用限制
"""

import os
import sys
import json
import uuid
import shutil
import platform
import subprocess
import time
from pathlib import Path
from typing import Dict, List, Optional

class CursorUnlimited:
    def __init__(self):
        self.system = platform.system()
        self.config_paths = self._get_config_paths()
        
    def _get_config_paths(self) -> Dict[str, str]:
        """获取不同操作系统下的Cursor配置路径"""
        if self.system == "Windows":
            base_path = os.path.expandvars("%APPDATA%\\Cursor")
        elif self.system == "Darwin":  # macOS
            base_path = os.path.expanduser("~/Library/Application Support/Cursor")
        else:  # Linux
            base_path = os.path.expanduser("~/.config/Cursor")
            
        return {
            "base": base_path,
            "storage": os.path.join(base_path, "User", "globalStorage", "storage.json"),
            "user_data": os.path.join(base_path, "User"),
            "logs": os.path.join(base_path, "logs")
        }
    
    def print_banner(self):
        """打印工具横幅"""
        banner = """
╔══════════════════════════════════════════════════════════════╗
║                    Cursor AI 无限使用工具                      ║
║                                                              ║
║  功能:                                                        ║
║  1. 重置试用期                                                ║
║  2. 生成新机器ID                                              ║
║  3. 清理用户数据                                              ║
║  4. 自动化重置流程                                            ║
║                                                              ║
║  注意: 仅供学习研究使用，请支持正版软件                        ║
╚══════════════════════════════════════════════════════════════╝
        """
        print(banner)
    
    def check_cursor_running(self) -> bool:
        """检查Cursor是否正在运行"""
        try:
            if self.system == "Windows":
                result = subprocess.run(
                    ["tasklist", "/FI", "IMAGENAME eq Cursor.exe"],
                    capture_output=True, text=True
                )
                return "Cursor.exe" in result.stdout
            else:
                result = subprocess.run(
                    ["pgrep", "-f", "cursor"],
                    capture_output=True, text=True
                )
                return bool(result.stdout.strip())
        except:
            return False
    
    def kill_cursor_process(self):
        """终止Cursor进程"""
        print("正在终止Cursor进程...")
        try:
            if self.system == "Windows":
                subprocess.run(["taskkill", "/F", "/IM", "Cursor.exe"], 
                             capture_output=True)
            else:
                subprocess.run(["pkill", "-f", "cursor"], 
                             capture_output=True)
            time.sleep(2)
            print("✓ Cursor进程已终止")
        except Exception as e:
            print(f"✗ 终止进程失败: {e}")
    
    def generate_new_machine_id(self) -> Dict[str, str]:
        """生成新的机器ID"""
        return {
            "telemetry.macMachineId": str(uuid.uuid4()),
            "telemetry.machineId": str(uuid.uuid4()),
            "telemetry.devDeviceId": str(uuid.uuid4())
        }
    
    def backup_config(self):
        """备份配置文件"""
        if os.path.exists(self.config_paths["storage"]):
            backup_path = self.config_paths["storage"] + ".backup"
            shutil.copy2(self.config_paths["storage"], backup_path)
            print(f"✓ 配置文件已备份到: {backup_path}")
    
    def modify_storage_config(self, new_ids: Dict[str, str]) -> bool:
        """修改storage.json配置文件"""
        try:
            storage_path = self.config_paths["storage"]
            
            if not os.path.exists(storage_path):
                print("✗ 配置文件不存在，创建新的配置文件")
                os.makedirs(os.path.dirname(storage_path), exist_ok=True)
                config = {}
            else:
                with open(storage_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
            
            # 更新机器ID
            for key, value in new_ids.items():
                config[key] = value
            
            # 清除试用相关的配置
            trial_keys = [
                "cursor.trial.startDate",
                "cursor.trial.endDate", 
                "cursor.trial.used",
                "cursor.usage.count",
                "cursor.usage.limit"
            ]
            
            for key in trial_keys:
                if key in config:
                    del config[key]
            
            with open(storage_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2)
            
            print("✓ 配置文件修改成功")
            return True
            
        except Exception as e:
            print(f"✗ 修改配置文件失败: {e}")
            return False
    
    def clear_user_data(self):
        """清理用户数据"""
        try:
            user_data_path = self.config_paths["user_data"]
            if os.path.exists(user_data_path):
                # 保留storage.json，删除其他文件
                for item in os.listdir(user_data_path):
                    item_path = os.path.join(user_data_path, item)
                    if item != "globalStorage" and os.path.isdir(item_path):
                        shutil.rmtree(item_path)
                        print(f"✓ 已清理: {item}")
            
            # 清理日志文件
            logs_path = self.config_paths["logs"]
            if os.path.exists(logs_path):
                shutil.rmtree(logs_path)
                print("✓ 日志文件已清理")
                
        except Exception as e:
            print(f"✗ 清理用户数据失败: {e}")
    
    def reset_trial(self) -> bool:
        """重置试用期的主要方法"""
        print("\n开始重置Cursor试用期...")
        
        # 1. 检查并终止Cursor进程
        if self.check_cursor_running():
            self.kill_cursor_process()
        
        # 2. 备份配置
        self.backup_config()
        
        # 3. 生成新的机器ID
        new_ids = self.generate_new_machine_id()
        print("✓ 新机器ID已生成")
        
        # 4. 修改配置文件
        if not self.modify_storage_config(new_ids):
            return False
        
        # 5. 清理用户数据
        self.clear_user_data()
        
        print("\n✓ 试用期重置完成！")
        print("请重新启动Cursor并登录您的账户")
        return True
    
    def show_menu(self):
        """显示主菜单"""
        while True:
            print("\n" + "="*50)
            print("请选择操作:")
            print("1. 重置试用期")
            print("2. 仅生成新机器ID")
            print("3. 清理用户数据")
            print("4. 检查Cursor状态")
            print("5. 恢复备份配置")
            print("0. 退出")
            print("="*50)
            
            choice = input("请输入选项 (0-5): ").strip()
            
            if choice == "1":
                self.reset_trial()
            elif choice == "2":
                ids = self.generate_new_machine_id()
                print("新生成的机器ID:")
                for key, value in ids.items():
                    print(f"  {key}: {value}")
            elif choice == "3":
                self.clear_user_data()
            elif choice == "4":
                running = self.check_cursor_running()
                print(f"Cursor运行状态: {'运行中' if running else '未运行'}")
                print(f"配置路径: {self.config_paths['base']}")
            elif choice == "5":
                self.restore_backup()
            elif choice == "0":
                print("感谢使用！")
                break
            else:
                print("无效选项，请重新选择")
    
    def restore_backup(self):
        """恢复备份配置"""
        backup_path = self.config_paths["storage"] + ".backup"
        if os.path.exists(backup_path):
            shutil.copy2(backup_path, self.config_paths["storage"])
            print("✓ 配置文件已恢复")
        else:
            print("✗ 未找到备份文件")

def main():
    """主函数"""
    try:
        tool = CursorUnlimited()
        tool.print_banner()
        
        # 检查配置路径是否存在
        if not os.path.exists(tool.config_paths["base"]):
            print(f"✗ 未找到Cursor配置目录: {tool.config_paths['base']}")
            print("请确保已安装Cursor编辑器")
            return
        
        tool.show_menu()
        
    except KeyboardInterrupt:
        print("\n\n程序被用户中断")
    except Exception as e:
        print(f"\n程序运行出错: {e}")

if __name__ == "__main__":
    main()
