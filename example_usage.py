#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cursor AI 无限使用工具 - 使用示例
演示如何使用API接口进行自动化重置
"""

import sys
import time
from cursor_pro_unlimited import CursorProUnlimited

def example_basic_reset():
    """示例1: 基础重置操作"""
    print("=== 示例1: 基础重置操作 ===")
    
    # 创建工具实例
    tool = CursorProUnlimited()
    
    # 检查Cursor状态
    if tool.check_cursor_running():
        print("检测到Cursor正在运行，正在关闭...")
        tool.force_kill_cursor()
    
    # 执行机器ID重置
    print("执行机器ID重置...")
    success = tool.method_machine_id_reset()
    
    if success:
        print("✅ 重置成功！请重新启动Cursor")
    else:
        print("❌ 重置失败，请尝试其他方法")

def example_complete_reset():
    """示例2: 完全重置操作"""
    print("\n=== 示例2: 完全重置操作 ===")
    
    tool = CursorProUnlimited()
    
    # 创建备份
    backup_dir = tool.create_backup()
    if backup_dir:
        print(f"✅ 备份已创建: {backup_dir}")
    
    # 强制终止Cursor
    tool.force_kill_cursor()
    
    # 执行完全重置
    print("执行完全重置...")
    success = tool.method_complete_reset()
    
    if success:
        print("✅ 完全重置成功！")
        print("所有配置已清除，请重新启动Cursor并登录")
    else:
        print("❌ 完全重置失败")
        if backup_dir:
            print(f"可以从备份恢复: {backup_dir}")

def example_auto_reset():
    """示例3: 自动化重置脚本"""
    print("\n=== 示例3: 自动化重置脚本 ===")
    
    tool = CursorProUnlimited()
    
    # 定义重置策略
    reset_methods = ["1", "2", "4"]  # 按优先级尝试不同方法
    
    for method in reset_methods:
        print(f"尝试方法 {method}: {tool.methods[method]}")
        
        # 强制终止Cursor
        tool.force_kill_cursor()
        
        # 执行重置
        success = False
        if method == "1":
            success = tool.method_machine_id_reset()
        elif method == "2":
            success = tool.method_config_cleanup()
        elif method == "4":
            success = tool.method_complete_reset()
        
        if success:
            print(f"✅ 方法 {method} 执行成功！")
            break
        else:
            print(f"❌ 方法 {method} 执行失败，尝试下一个方法...")
            time.sleep(2)
    
    if not success:
        print("❌ 所有重置方法都失败了")

def example_scheduled_reset():
    """示例4: 定时重置任务"""
    print("\n=== 示例4: 定时重置任务 ===")
    
    tool = CursorProUnlimited()
    
    # 模拟定时任务
    reset_interval = 5  # 5秒间隔（实际使用中可以设置为小时或天）
    max_cycles = 3      # 最大循环次数
    
    for cycle in range(1, max_cycles + 1):
        print(f"\n--- 第 {cycle} 次定时重置 ---")
        
        # 检查Cursor状态
        if tool.check_cursor_running():
            print("检测到Cursor运行中，执行重置...")
            
            # 执行重置
            tool.force_kill_cursor()
            success = tool.method_machine_id_reset()
            
            if success:
                print(f"✅ 第 {cycle} 次重置成功")
            else:
                print(f"❌ 第 {cycle} 次重置失败")
        else:
            print("Cursor未运行，跳过重置")
        
        # 等待下次重置
        if cycle < max_cycles:
            print(f"等待 {reset_interval} 秒后进行下次检查...")
            time.sleep(reset_interval)

def example_batch_operations():
    """示例5: 批量操作"""
    print("\n=== 示例5: 批量操作 ===")
    
    tool = CursorProUnlimited()
    
    operations = [
        ("检查状态", lambda: tool.check_status()),
        ("生成机器指纹", lambda: print("新指纹:", tool.generate_machine_fingerprint())),
        ("清理用户数据", lambda: tool.method_user_data_reset()),
        ("检查目录大小", lambda: print(f"配置目录大小: {tool.get_dir_size(tool.config_paths['base']):.2f} MB"))
    ]
    
    for name, operation in operations:
        print(f"\n执行操作: {name}")
        try:
            operation()
            print(f"✅ {name} 完成")
        except Exception as e:
            print(f"❌ {name} 失败: {e}")
        time.sleep(1)

def example_error_handling():
    """示例6: 错误处理和恢复"""
    print("\n=== 示例6: 错误处理和恢复 ===")
    
    tool = CursorProUnlimited()
    
    try:
        # 尝试执行可能失败的操作
        print("尝试执行重置操作...")
        
        # 创建备份
        backup_dir = tool.create_backup()
        
        # 执行重置
        success = tool.method_machine_id_reset()
        
        if not success:
            raise Exception("重置操作失败")
        
        print("✅ 操作成功完成")
        
    except Exception as e:
        print(f"❌ 操作失败: {e}")
        
        # 错误恢复
        if backup_dir:
            print("尝试从备份恢复...")
            try:
                # 这里可以添加恢复逻辑
                print("✅ 已从备份恢复")
            except Exception as restore_error:
                print(f"❌ 恢复失败: {restore_error}")

def main():
    """主函数 - 运行所有示例"""
    print("Cursor AI 无限使用工具 - 使用示例")
    print("=" * 50)
    
    examples = [
        example_basic_reset,
        example_complete_reset, 
        example_auto_reset,
        example_scheduled_reset,
        example_batch_operations,
        example_error_handling
    ]
    
    for i, example in enumerate(examples, 1):
        try:
            example()
        except KeyboardInterrupt:
            print(f"\n示例 {i} 被用户中断")
            break
        except Exception as e:
            print(f"\n示例 {i} 执行出错: {e}")
        
        # 询问是否继续
        if i < len(examples):
            try:
                continue_choice = input(f"\n按回车继续下一个示例，或输入 'q' 退出: ").strip()
                if continue_choice.lower() == 'q':
                    break
            except KeyboardInterrupt:
                break
    
    print("\n所有示例演示完成！")

if __name__ == "__main__":
    main()
