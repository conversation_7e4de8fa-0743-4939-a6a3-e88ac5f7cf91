@echo off
chcp 65001 >nul
title Cursor快速重置工具

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    Cursor快速重置工具                        ║
echo ║                                                              ║
echo ║  一键重置Cursor试用期，简单快速！                             ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM 检查Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未安装Python，请先安装Python 3.6+
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

REM 检查工具文件
if not exist "cursor_pro_unlimited.py" (
    echo ❌ 未找到工具文件，请确保所有文件在同一目录
    pause
    exit /b 1
)

echo ⚡ 正在执行快速重置...
echo.

REM 强制关闭Cursor
echo 🔄 关闭Cursor进程...
taskkill /F /IM Cursor.exe >nul 2>&1
timeout /t 2 >nul

REM 执行重置
echo 🔧 执行机器ID重置...
python -c "
import sys
sys.path.append('.')
from cursor_pro_unlimited import CursorProUnlimited

tool = CursorProUnlimited()
print('正在生成新的机器指纹...')
success = tool.method_machine_id_reset()

if success:
    print('✅ 重置成功！')
    print('请重新启动Cursor并登录账户')
else:
    print('❌ 重置失败，请尝试完全重置法')
"

echo.
echo 🎉 快速重置完成！
echo.
echo 📋 下一步操作：
echo   1. 重新启动Cursor编辑器
echo   2. 登录您的账户
echo   3. 享受新的试用期
echo.

set /p start_cursor="是否现在启动Cursor？ (y/N): "
if /i "%start_cursor%"=="y" (
    start "" "cursor" 2>nul || echo ⚠️  请手动启动Cursor
)

echo.
pause
