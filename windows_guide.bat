@echo off
chcp 65001 >nul
title Cursor AI 无限使用工具 - Windows使用指南

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                Windows用户使用指南                           ║
echo ║                                                              ║
echo ║  这个脚本将指导您完成Cursor重置的全过程                       ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 📋 使用步骤：
echo.
echo 1️⃣  检查Python环境
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未检测到Python
    echo 请先安装Python: https://www.python.org/downloads/
    echo 安装时记得勾选 "Add Python to PATH"
    pause
    exit /b 1
) else (
    echo ✅ Python环境正常
)

echo.
echo 2️⃣  检查Cursor进程
tasklist /FI "IMAGENAME eq Cursor.exe" 2>nul | find /I "Cursor.exe" >nul
if %errorlevel% equ 0 (
    echo ⚠️  检测到Cursor正在运行
    echo 建议先关闭Cursor编辑器
    set /p choice="是否继续？工具会自动关闭Cursor (y/N): "
    if /i not "%choice%"=="y" (
        echo 操作已取消
        pause
        exit /b 0
    )
) else (
    echo ✅ Cursor未运行
)

echo.
echo 3️⃣  选择重置方法
echo.
echo 推荐的重置方法：
echo   🥇 机器ID重置法 (成功率高，风险低)
echo   🥈 完全重置法 (最彻底，适合其他方法失败时使用)
echo   🥉 配置文件清理法 (轻度重置)
echo.

set /p method="请选择重置方法 (1=机器ID重置, 4=完全重置, 其他=取消): "

if "%method%"=="1" (
    echo.
    echo 🔄 即将执行机器ID重置法...
    echo 这个方法会：
    echo   • 生成新的机器指纹
    echo   • 修改telemetry相关ID  
    echo   • 清除试用期记录
    echo.
) else if "%method%"=="4" (
    echo.
    echo 💥 即将执行完全重置法...
    echo 这个方法会：
    echo   • 删除所有Cursor配置文件
    echo   • 重新创建配置目录
    echo   • 完全清除使用记录
    echo.
    echo ⚠️  注意：这会删除您的所有Cursor设置！
) else (
    echo 操作已取消
    pause
    exit /b 0
)

set /p confirm="确认执行重置操作？ (y/N): "
if /i not "%confirm%"=="y" (
    echo 操作已取消
    pause
    exit /b 0
)

echo.
echo 4️⃣  执行重置操作
echo.

REM 创建临时Python脚本来执行重置
echo import sys > temp_reset.py
echo sys.path.append('.') >> temp_reset.py
echo from cursor_pro_unlimited import CursorProUnlimited >> temp_reset.py
echo. >> temp_reset.py
echo tool = CursorProUnlimited() >> temp_reset.py
echo print("正在执行重置...") >> temp_reset.py
echo tool.force_kill_cursor() >> temp_reset.py

if "%method%"=="1" (
    echo success = tool.method_machine_id_reset() >> temp_reset.py
) else (
    echo success = tool.method_complete_reset() >> temp_reset.py
)

echo. >> temp_reset.py
echo if success: >> temp_reset.py
echo     print("✅ 重置成功！") >> temp_reset.py
echo     print("请重新启动Cursor并登录您的账户") >> temp_reset.py
echo else: >> temp_reset.py
echo     print("❌ 重置失败，请尝试其他方法") >> temp_reset.py

REM 执行重置
python temp_reset.py

REM 清理临时文件
del temp_reset.py >nul 2>&1

echo.
echo 5️⃣  后续步骤
echo.
echo 📌 重置完成后请按以下步骤操作：
echo.
echo   1. 重新启动Cursor编辑器
echo   2. 登录您的账户（可以使用相同账户）
echo   3. 检查是否获得新的试用期
echo.
echo 💡 小贴士：
echo   • 如果重置失败，可以尝试其他重置方法
echo   • 建议在虚拟机中测试工具效果
echo   • 请支持正版软件开发
echo.

set /p open_cursor="是否现在启动Cursor？ (y/N): "
if /i "%open_cursor%"=="y" (
    echo 正在启动Cursor...
    start "" "cursor" 2>nul || (
        echo ⚠️  无法自动启动Cursor，请手动打开
    )
)

echo.
echo 🎉 使用指南完成！
echo.
echo 如果遇到问题，请：
echo   1. 检查是否以管理员身份运行
echo   2. 确保Python环境正确
echo   3. 尝试不同的重置方法
echo   4. 查看README.md获取更多帮助
echo.

pause
