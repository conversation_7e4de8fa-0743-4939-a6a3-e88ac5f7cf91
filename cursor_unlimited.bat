@echo off
chcp 65001 >nul
title Cursor AI 无限使用工具

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    Cursor AI 无限使用工具                      ║
echo ║                                                              ║
echo ║  正在启动Python脚本...                                        ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ✗ 未检测到Python，请先安装Python 3.6+
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

REM 运行Python脚本
python cursor_unlimited.py

pause
